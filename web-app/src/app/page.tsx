"use client"; // This page now needs client-side interactivity for state

import { Navbar } from "@/components/layout/Navbar";
import { FaqSection } from "@/components/sections/FaqSection";
import { FeaturesSection } from "@/components/sections/FeaturesSection";
import { GenerationForm, GenerationFormProps } from "@/components/sections/GenerationForm";
import { HistoryItem, HistorySection } from "@/components/sections/HistorySection";
import { InspirationGallerySection } from "@/components/sections/InspirationGallerySection";
// import { getServerSession } from "next-auth/next"; // Cannot use in "use client" component directly for initial props in the same way
import { Session } from "next-auth";
import { useSession } from "next-auth/react"; // For client-side session access if needed
import { useState } from "react";


// Define a type for the items that will be displayed in the history section
// This can be either a loading placeholder or a completed image result
export type DisplayItem = HistoryItem | LoadingPlaceholderItem;

export interface LoadingPlaceholderItem {
  id: string; // Unique ID for the placeholder, can be timestamp
  type: 'loading';
  prompt: string;
  uploadedImage?: string | null;
  timestamp: number;
}

// Helper function to get initial session if needed, though Navbar handles its own SSR session
// This is tricky because Home is now a client component.
// We might need to pass initialSession from a server component parent if strict SSR for Navbar is critical here.
// For now, Navbar's existing SSR logic might suffice.

export default function Home() {
  // const session = await getServerSession(authOptions); // Cannot use await in client component
  const { data: session } = useSession(); // Use client-side session
  const [historyItems, setHistoryItems] = useState<DisplayItem[]>([]);
  const [isHistoryPanelVisible, setIsHistoryPanelVisible] = useState(false);

  // useEffect(() => {
  //   // Commented out: Load history from localStorage on component mount
  //   // const storedHistory = localStorage.getItem('generationHistory');
  //   // if (storedHistory) {
  //   //   try {
  //   //     const parsedHistory: DisplayItem[] = JSON.parse(storedHistory);
  //   //     // Filter out any old loading placeholders that might have persisted
  //   //     setHistoryItems(parsedHistory.filter(item => item.type !== 'loading'));
  //   //   } catch (error) {
  //   //     console.error("Failed to parse history from localStorage", error);
  //   //     setHistoryItems([]);
  //   //   }
  //   // }
  // }, []);

  // useEffect(() => {
  //   // Commented out: Save history to localStorage whenever it changes
  //   // // Don't save loading placeholders to localStorage
  //   // const itemsToSave = historyItems.filter(item => item.type !== 'loading');
  //   // if (itemsToSave.length > 0) {
  //   //   localStorage.setItem('generationHistory', JSON.stringify(itemsToSave));
  //   // } else {
  //   //   // If history is empty (after filtering out loading), remove from localStorage
  //   //   localStorage.removeItem('generationHistory');
  //   // }
  // }, [historyItems]);


  const handleGenerationStart: GenerationFormProps['onGenerationStart'] = (data) => {
    const newPlaceholder: LoadingPlaceholderItem = {
      id: data.timestamp.toString(), // Use timestamp as a unique ID for the placeholder
      type: 'loading',
      prompt: data.prompt,
      uploadedImage: data.uploadedImage,
      timestamp: data.timestamp,
    };
    setHistoryItems(prevItems => [newPlaceholder, ...prevItems]);
    if (!isHistoryPanelVisible) {
      setIsHistoryPanelVisible(true);
    }
  };

  const handleImageGenerated: GenerationFormProps['onImageGenerated'] = (imageData) => {
    const newItem: HistoryItem = {
      id: imageData.timestamp.toString(), // Use the same timestamp ID
      type: 'image',
      base64: imageData.base64,
      contentType: imageData.contentType,
      prompt: imageData.prompt,
      uploadedImage: imageData.uploadedImage,
      timestamp: imageData.timestamp,
    };
    setHistoryItems(prevItems =>
      prevItems.map(item => (item.id === newItem.id && item.type === 'loading' ? newItem : item))
    );
  };

  return (
    <div className="flex flex-col min-h-screen bg-background text-foreground">
      <Navbar initialSession={session as Session | null} /> {/* Pass client-side session */}
      {/* Ensure main provides the overall padding for "少量边距" and does not center its direct children unless they opt-in with mx-auto */}
      <main className="flex-1 flex flex-col w-full py-6 px-3 sm:px-6 md:py-16 md:px-8">
        {/* Hero Section - Uses mx-auto for centering within the padded main container */}
        <div className={`w-full max-w-5xl text-center mx-auto ${isHistoryPanelVisible && historyItems.length > 0 ? 'mb-8' : 'mb-12'}`}>
          <h1 className="text-5xl font-extrabold tracking-normal text-primary sm:text-6xl md:text-7xl flex items-center justify-center">
            <img src="/favicon.svg" alt="LuckyX AI Logo" className="h-12 w-12 mr-4 sm:h-14 sm:w-14 md:h-16 md:w-16" />
            LuckyX AI
          </h1>
          <p className="mt-6 text-lg leading-relaxed text-muted-foreground sm:text-xl sm:max-w-2xl mx-auto md:text-2xl"> {/* Removed lg:mx-0 and ensured mx-auto is present */}
            ✨ 告别复杂提示词，一键释放 AI 顶尖图像创作力
            <br />
            ✨ 无缝融合生成与编辑，让您的创意立即成为现实
          </p>
          <div className="mt-8 flex flex-wrap justify-center gap-3">
            <span className="bg-primary/10 text-primary text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-primary/20 transition-all cursor-default animate-float-1">SOTA</span>
            <span className="bg-secondary/10 text-secondary text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-secondary/20 transition-all cursor-default animate-float-2">秒级生成</span>
            <span className="bg-accent/10 text-accent text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-accent/20 transition-all cursor-default animate-float-3">零门槛创作</span>
            <span className="bg-[var(--chart-4)]/10 text-[var(--chart-4)] text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-[var(--chart-4)]/20 transition-all cursor-default animate-float-4">一体化编辑</span>
            <span className="bg-[var(--chart-5)]/10 text-[var(--chart-5)] text-sm font-semibold px-4 py-2 rounded-full shadow-md hover:bg-[var(--chart-5)]/20 transition-all cursor-default animate-float-5">风格转换</span>
          </div>
        </div>

        {/* Main Content Area: Conditional Layout */}
        {isHistoryPanelVisible && historyItems.length > 0 ? (
          // --- 双栏布局 ---
          // This container is w-full to use the padding from <main>.
          // Using CSS Grid for better layout control
          <div className="w-full max-w-7xl grid grid-cols-1 md:grid-cols-12 gap-6 lg:gap-8 px-4 sm:px-6 lg:px-8">
            <div className="md:col-span-7 lg:col-span-7"> {/* 左栏：创作区 - 稍微增加宽度 */}
              <GenerationForm
                onGenerationStart={handleGenerationStart}
                onImageGenerated={handleImageGenerated}
                isInTwoColumnLayout={true}
              />
            </div>
            {/* Right Column: History Section - 占据剩余空间 */}
            <div className="md:col-span-5 lg:col-span-5 rounded-lg border border-border bg-card shadow-xl p-4 sm:p-6 md:p-8 md:max-h-[calc(100vh-220px)] md:overflow-y-auto scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent">
              <HistorySection items={historyItems} />
            </div>
          </div>
        ) : (
          // --- 单栏布局 (初始状态) ---
          // This container is w-full, allowing GenerationForm to use the padding from <main>
          // GenerationForm itself should be w-full internally to achieve the wide look.
          <div className="w-full">
            <GenerationForm
              onGenerationStart={handleGenerationStart}
              onImageGenerated={handleImageGenerated}
              isInTwoColumnLayout={false}
            />
            {/* HistorySection is not rendered here in initial state */}
          </div>
        )}

        {/* Other page sections like Features, FAQ etc. - these also use mx-auto for centering */}
        <div className="w-full max-w-5xl items-center flex flex-col mt-16 mx-auto">
          <FeaturesSection />
          <InspirationGallerySection />
          <FaqSection />
        </div>

      </main>
      <footer className="py-6 md:px-8 md:py-0 bg-background border-t border-border">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
          <p className="text-balance text-center text-sm leading-loose text-muted-foreground md:text-left">
            © {new Date().getFullYear()} LuckyX AI. 保留所有权利.
          </p>
        </div>
      </footer>
    </div>
  );
}
