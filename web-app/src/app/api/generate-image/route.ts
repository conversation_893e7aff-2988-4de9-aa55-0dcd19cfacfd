import { fal } from '@fal-ai/client';
import { NextRequest, NextResponse } from 'next/server';

// Ensure FAL_KEY is set in your .env.local file
// FAL_KEY="your_fal_api_key"

// Define a type for the expected request body
interface GenerateImageRequestBody {
    prompt: string;
    image_size?: string; // e.g., "square_hd", "portrait_4_3"
    isHighQuality?: boolean;
    uploadedImage?: string; // Base64 encoded image
}

// Define a mapping from frontend image size labels to Fal.ai expected values
const imageSizeMap: Record<string, string | { width: number; height: number }> = {
    'Square': 'square', // Example, adjust based on actual Fal.ai values
    'Square HD': 'square_hd',
    'Portrait 3:4': 'portrait_4_3',
    'Portrait 9:16': 'portrait_16_9',
    'Landscape 4:3': 'landscape_4_3',
    'Landscape 16:9': 'landscape_16_9',
    // Add more mappings if needed, or handle custom sizes
};

export async function POST(request: NextRequest) {
    try {
        const body = await request.json() as GenerateImageRequestBody;
        const { prompt, image_size: frontendImageSize, isHighQuality, uploadedImage } = body;

        if (!prompt) {
            return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
        }

        let modelName: string;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const falInput: Record<string, any> = {
            enable_safety_checker: false,
            sync_mode: true, // Request synchronous processing to get image data directly
            output_format: 'jpeg', // Request JPEG format
        };

        if (uploadedImage) {
            // Image-to-Image
            modelName = isHighQuality ? 'fal-ai/step1x-edit' : 'fal-ai/hidream-e1-full';
            falInput.prompt = prompt; // For step1x-edit, prompt is the edit instruction
            falInput.edit_instruction = prompt; // For hidream-e1-full
            falInput.image_url = uploadedImage; // Base64 data URI
            // hidream-e1-full specific default, can be overridden if needed
            if (modelName === 'fal-ai/hidream-e1-full') {
                falInput.negative_prompt = falInput.negative_prompt || "low resolution, blur";
                falInput.guidance_scale = falInput.guidance_scale || 3.5;
                falInput.image_guidance_scale = falInput.image_guidance_scale || 2;
            }
        } else {
            // Text-to-Image
            modelName = isHighQuality ? 'fal-ai/hidream-i1-dev' : 'fal-ai/hidream-i1-fast';
            falInput.prompt = prompt;
            if (frontendImageSize && imageSizeMap[frontendImageSize]) {
                falInput.image_size = imageSizeMap[frontendImageSize];
            } else {
                falInput.image_size = 'square_hd'; // Default if not provided or mapping not found
            }
        }

        console.log(`Calling Fal.ai model: ${modelName} with input:`, JSON.stringify(falInput, null, 2));

        const result: any = await fal.subscribe(modelName, {
            // const result: any = await fal.run(modelName, { // .run is for sync, .subscribe for async with updates
            input: falInput,
            logs: true, // Optional: to see logs during development
            onQueueUpdate: (update) => {
                if (update.status === 'IN_PROGRESS') {
                    // console.log('Fal.ai Queue Update:', update.logs);
                }
            },
        });

        console.log('Fal.ai raw result:', JSON.stringify(result, null, 2));

        // According to user's note: "images 字段是包在 data 字段里面，url 也不是图片链接"
        // And for sync_mode, the image data might be directly in `images[0].content` or similar
        // The Fal.ai documentation for sync_mode usually implies the image data is returned directly.
        // Let's assume `result.images[0].content` for Base64 data or `result.images[0].url` is a data URI.
        // The `fal.subscribe` method returns the full result which might be nested.
        // The actual image data might be in `result.images[0].content` if it's raw bytes,
        // or `result.images[0].url` if it's a data URI when sync_mode is true.
        // The provided documentation for hidream-i1-fast output schema shows:
        // "images": [ { "url": "", "content_type": "image/jpeg" } ]
        // For sync_mode, the 'url' field might contain the base64 data URI.
        // Or, if the SDK handles it differently, it might be in a `content` field or `file_data`.
        // Let's prioritize `file_data` if present, then `content`, then `url` (assuming it's a data URI).

        let imageBase64: string | undefined;
        let contentType: string = 'image/jpeg'; // Default

        let images = null;
        images = result?.data?.images;

        if (images && images.length > 0) {
            const imageInfo = images[0];
            contentType = imageInfo.content_type || contentType;

            // Check for direct base64 content (common with some SDKs/sync modes)
            // The provided docs for step1x-edit show `file_data` in the Image type.
            if (imageInfo.file_data) {
                imageBase64 = imageInfo.file_data;
            } else if (imageInfo.content) { // Some SDKs might use 'content' for raw bytes
                // If content is ArrayBuffer or Uint8Array, convert to base64
                if (imageInfo.content instanceof ArrayBuffer || imageInfo.content instanceof Uint8Array) {
                    const buffer = Buffer.from(imageInfo.content);
                    imageBase64 = buffer.toString('base64');
                } else if (typeof imageInfo.content === 'string') {
                    // Assuming it might already be base64, or needs prefix
                    imageBase64 = imageInfo.content.startsWith('data:') ? imageInfo.content.split(',')[1] : imageInfo.content;
                }
            } else if (imageInfo.url && imageInfo.url.startsWith('data:')) {
                // If URL is a data URI, extract base64 part
                imageBase64 = imageInfo.url.split(',')[1];
            }

            if (!imageBase64) {
                console.error('Failed to extract image data from Fal.ai response:', result);
                return NextResponse.json({ error: '系统忙碌，请稍后重试。' }, { status: 500 });
            }

            return NextResponse.json({ imageBase64, contentType });
        } else {
            console.error('No images found in Fal.ai response:', result);
            return NextResponse.json({ error: '系统忙碌，请稍后重试。' }, { status: 500 });
        }

    } catch (error) {
        console.error('Error calling Fal.ai API:', error);
        let errorMessage = '系统忙碌，请稍后重试。';
        if (error instanceof Error) {
            // You might want to log error.message for internal debugging
            // but not expose it to the client.
            console.error('Fal.ai Error Message:', error.message);
        }
        return NextResponse.json({ error: errorMessage }, { status: 500 });
    }
}
