"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Download, Image as ImageIconPlaceholder, Loader2 } from 'lucide-react';
import { useState } from 'react';

// Type definitions (can be moved to a shared types file later)
export interface HistoryItem {
    id: string;
    type: 'image';
    base64: string;
    contentType: string;
    prompt: string;
    uploadedImage?: string | null;
    timestamp: number;
}

export interface LoadingPlaceholderItem {
    id: string;
    type: 'loading';
    prompt: string;
    uploadedImage?: string | null;
    timestamp: number;
}

export type DisplayItem = HistoryItem | LoadingPlaceholderItem;

interface HistorySectionProps {
    items: DisplayItem[];
}

// Helper to format timestamp
const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN', { hour12: false });
};

// Placeholder for the "Generate 5x faster" button visual
const FasterGenerationButtonVisual = () => (
    <div className="mt-4 inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-orange-800 bg-orange-200 border border-transparent rounded-md shadow-sm">
        <svg className="w-5 h-5 mr-2 -ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path></svg>
        生成速度快 5 倍
    </div>
);


export const HistorySection: React.FC<HistorySectionProps> = ({ items }) => {
    const [selectedImage, setSelectedImage] = useState<HistoryItem | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    const openImageModal = (item: HistoryItem) => {
        setSelectedImage(item);
        setIsModalOpen(true);
    };

    const downloadImage = (item: HistoryItem) => {
        const link = document.createElement('a');
        link.href = `data:${item.contentType};base64,${item.base64}`;
        link.download = `luckyx_ai_image_${item.timestamp}.${item.contentType.split('/')[1] || 'jpg'}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (!items || items.length === 0) {
        return (
            <div className="w-full">
                <h3 className="mb-6 text-xl font-semibold text-center text-foreground md:text-left">
                    创作历史
                </h3>
                <div className="rounded-lg border border-border bg-background/50 p-6 text-center text-muted-foreground">
                    <p>这里将显示您生成的图像历史记录。</p>
                    <p className="text-sm mt-2">(历史记录将保存在浏览器本地)</p>
                </div>
            </div>
        );
    }
    // When this component is part of the two-column layout, its parent div in page.tsx will control width and scrolling.
    return (
        <div className="w-full">
            <h3 className="mb-6 text-xl font-semibold text-center text-foreground sticky top-0 bg-background/80 backdrop-blur-sm py-2 z-10 md:text-left">
                创作历史
            </h3>
            {/* Grid layout for history items - single column in sidebar for better display */}
            <div className="grid grid-cols-1 gap-4">
                {items.map((item) => (
                    // Each item in single column layout
                    <div
                        key={item.id}
                        className="bg-card border border-border rounded-lg shadow-lg p-4 flex flex-col"
                    >
                        {item.type === 'loading' ? (
                            <div className="flex flex-col items-center justify-center text-center text-muted-foreground py-8">
                                <Loader2 className="h-10 w-10 animate-spin text-primary mb-3" />
                                <p className="text-sm font-medium">正在生成图像...</p>
                                <p className="text-xs mt-2 text-center px-2" title={item.prompt}>
                                    提示词: {item.prompt.length > 50 ? item.prompt.substring(0, 50) + '...' : item.prompt}
                                </p>
                                {item.uploadedImage && <p className="text-xs mt-1">(基于上传图片)</p>}
                                <p className="text-xs mt-2 text-primary">预计时间: 20秒</p>
                                <div className="mt-3 scale-90">
                                    <FasterGenerationButtonVisual />
                                </div>
                            </div>
                        ) : (
                            // Content for generated image - horizontal layout for sidebar
                            <div className="flex gap-3">
                                <div
                                    className="w-20 h-20 bg-muted rounded-md overflow-hidden cursor-pointer hover:opacity-80 transition-opacity relative group flex-shrink-0"
                                    onClick={() => openImageModal(item)}
                                >
                                    <img
                                        src={`data:${item.contentType};base64,${item.base64}`}
                                        alt={item.prompt}
                                        className="w-full h-full object-cover rounded-md"
                                    />
                                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                                        <ImageIconPlaceholder className="w-5 h-5 text-white opacity-0 group-hover:opacity-70 transition-opacity" />
                                    </div>
                                </div>
                                <div className="flex flex-col flex-grow justify-between min-w-0">
                                    <div>
                                        <p className="text-xs text-muted-foreground">{formatTimestamp(item.timestamp)}</p>
                                        <p className="mt-0.5 text-sm font-semibold text-foreground leading-tight break-words line-clamp-2" title={item.prompt}>
                                            {item.prompt}
                                        </p>
                                        {item.uploadedImage && (
                                            <p className="text-xs text-muted-foreground mt-0.5">
                                                (基于上传图片)
                                            </p>
                                        )}
                                    </div>
                                    {/* Removed download button from list item */}
                                </div>
                            </div>
                        )}
                    </div>
                ))}
            </div>

            {selectedImage && (
                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                    <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl xl:max-w-5xl max-h-[90vh] flex flex-col p-4 gap-4"> {/* Added gap and padding to content */}
                        {/* DialogHeader removed/modified to not show "查看图片" */}
                        {/* DialogTitle is part of DialogHeader, so we need a new way to show prompt if header is gone, or restyle title */}

                        {/* Prompt display - ensuring it's visible, wraps, and doesn't interfere with a potential top-right close button (Shadcn's DialogClose) */}
                        <div className="pr-8"> {/* Add padding to the right if DialogClose is typically there */}
                            <p className="text-sm text-muted-foreground whitespace-normal break-words">
                                {selectedImage.prompt}
                            </p>
                        </div>

                        <div className="flex-grow overflow-auto flex items-center justify-center bg-muted/10 rounded relative group/modalimg"> {/* Added relative and group for download button */}
                            <img
                                src={`data:${selectedImage.contentType};base64,${selectedImage.base64}`}
                                alt={selectedImage.prompt}
                                className="max-w-full max-h-[calc(80vh-100px)] object-contain rounded" // Adjusted max-h
                            />
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute bottom-2 right-2 opacity-70 group-hover/modalimg:opacity-100 transition-opacity bg-background/50 hover:bg-background/80"
                                onClick={() => downloadImage(selectedImage)}
                                title="下载图片"
                            >
                                <Download className="h-5 w-5" />
                            </Button>
                        </div>
                        {/* DialogFooter removed */}
                    </DialogContent>
                </Dialog>
            )}
        </div>
    );
};
